# 项目介绍

这是一个通过 Webhook 获取投递的监控告警数据的处理项目，将接收到的监控告警数据先写入 MongoDB 中，然后根据其告警等级，选择不同的通知渠道并发送告警。

# 任务

1、使用 fasthttp 来创建对应的 HTTP Server，并且处理 HTTP 请求，并且需要支持多种告警项目的接入
2、首先 POST 请求传入的 Body 可能会有很多种类，比如开源 AlertManager、阿里云 SLS 等等，所以你需要通过项目的维度，先实现针对 AlertManager 格式的解析，其他的可以先不实现。
3、所有的配置内容，我希望都使用 YAML 的方式来配置，而不是直接写在代码中
4、你还需要考虑到，针对同一条告警，会有两条，如果是 firing，那么还可能会有相同的 fingerprint 的一条 resolved 的数据会进来
5、通知渠道也会有多种，你也需要根据项目的维度来实现
6、所有的代码和模块和目录结构，都遵循项目维度，并且具备灵活通用性要求

# Docs
[飞书发送消息内容结构](https://open.feishu.cn/document/server-docs/im-v1/message-content-description/create_json)

# Demo
飞书发送消息 Go SDK
```
package main

import (
	"context"
	"fmt"
	"github.com/larksuite/oapi-sdk-go/v3"
	"github.com/larksuite/oapi-sdk-go/v3/core"
	"github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
)

// SDK 使用文档：https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/server-side-sdk/golang-sdk-guide/preparations
// 复制该 Demo 后, 需要将 "YOUR_APP_ID", "YOUR_APP_SECRET" 替换为自己应用的 APP_ID, APP_SECRET.
// 以下示例代码默认根据文档示例值填充，如果存在代码问题，请在 API 调试台填上相关必要参数后再复制代码使用
func main() {
	// 创建 Client
	client := lark.NewClient("YOUR_APP_ID", "YOUR_APP_SECRET")
	// 创建请求对象
	req := larkim.NewCreateMessageReqBuilder().
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(`ou_7d8a6e6df7621556ce0d21922b676706ccs`).
			MsgType(`text`).
			Content(`{"text":"test content"}`).
			Uuid(`选填，每次调用前请更换，如a0d69e20-1dd1-458b-k525-dfeca4015204`).
			Build()).
		Build()

	// 发起请求
	resp, err := client.Im.V1.Message.Create(context.Background(), req)

	// 处理错误
	if err != nil {
		fmt.Println(err)
		return
	}

	// 服务端错误处理
	if !resp.Success() {
		fmt.Printf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return
	}

	// 业务处理
	fmt.Println(larkcore.Prettify(resp))
}
```