package server

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/valyala/fasthttp"

	"alarm_distribution/internal/config"
	"alarm_distribution/internal/handlers"
	"alarm_distribution/internal/models"
	"alarm_distribution/internal/notifications"
	"alarm_distribution/internal/processor"
	"alarm_distribution/internal/storage"
)

// Server represents the HTTP server
type Server struct {
	config      *config.Config
	server      *fasthttp.Server
	storage     *storage.MongoStorage
	processor   *processor.AlertProcessor
	parsers     map[string]models.AlertParser
	channels    map[string]models.NotificationChannel
}

// New creates a new server instance
func New(cfg *config.Config) *Server {
	// Initialize MongoDB storage
	mongoStorage, err := storage.NewMongoStorage(&cfg.MongoDB)
	if err != nil {
		log.Fatalf("Failed to initialize MongoDB storage: %v", err)
	}

	// Initialize parsers
	parsers := make(map[string]models.AlertParser)
	parsers["alertmanager"] = parsers.NewAlertManagerParser()

	// Initialize notification channels
	channels := make(map[string]models.NotificationChannel)
	if cfg.Notifications.Feishu.Enabled {
		feishuChannel := notifications.NewFeishuChannel(&cfg.Notifications.Feishu)
		channels["feishu"] = feishuChannel
	}

	// Initialize alert processor
	alertProcessor := processor.NewAlertProcessor(mongoStorage, channels, cfg)

	srv := &Server{
		config:    cfg,
		storage:   mongoStorage,
		processor: alertProcessor,
		parsers:   parsers,
		channels:  channels,
	}

	// Create fasthttp server
	srv.server = &fasthttp.Server{
		Handler:      srv.router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	return srv
}

// router handles HTTP routing
func (s *Server) router(ctx *fasthttp.RequestCtx) {
	path := string(ctx.Path())
	method := string(ctx.Method())

	// Health check endpoint
	if path == "/health" && method == "GET" {
		s.handleHealth(ctx)
		return
	}

	// Webhook endpoints for different projects
	for projectName, projectConfig := range s.config.Projects {
		if path == projectConfig.Endpoint && method == "POST" {
			s.handleWebhook(ctx, projectName, projectConfig)
			return
		}
	}

	// Default 404 handler
	ctx.SetStatusCode(fasthttp.StatusNotFound)
	ctx.SetBodyString("Not Found")
}

// handleHealth handles health check requests
func (s *Server) handleHealth(ctx *fasthttp.RequestCtx) {
	ctx.SetStatusCode(fasthttp.StatusOK)
	ctx.SetContentType("application/json")
	ctx.SetBodyString(`{"status":"ok","timestamp":"` + time.Now().Format(time.RFC3339) + `"}`)
}

// handleWebhook handles webhook requests for different projects
func (s *Server) handleWebhook(ctx *fasthttp.RequestCtx, projectName string, projectConfig config.ProjectConfig) {
	// Get the appropriate parser
	parser, exists := s.parsers[projectConfig.Type]
	if !exists {
		log.Printf("No parser found for project type: %s", projectConfig.Type)
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		ctx.SetBodyString("Unsupported project type")
		return
	}

	// Create webhook handler
	handler := handlers.NewWebhookHandler(s.processor, parser)
	handler.Handle(ctx, projectName)
}

// Start starts the HTTP server
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%d", s.config.Server.Host, s.config.Server.Port)
	log.Printf("Starting server on %s", addr)
	return s.server.ListenAndServe(addr)
}

// Stop stops the HTTP server
func (s *Server) Stop() error {
	log.Println("Stopping server...")
	
	// Shutdown server
	if err := s.server.Shutdown(); err != nil {
		log.Printf("Error shutting down server: %v", err)
	}

	// Close MongoDB connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := s.storage.Close(ctx); err != nil {
		log.Printf("Error closing MongoDB connection: %v", err)
		return err
	}

	return nil
}
