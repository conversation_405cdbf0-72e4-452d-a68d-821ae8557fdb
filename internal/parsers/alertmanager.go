package parsers

import (
	"encoding/json"
	"fmt"
	"time"

	"alarm_distribution/internal/models"
)

// AlertManagerParser implements the AlertParser interface for AlertManager format
type AlertManagerParser struct{}

// AlertManagerWebhook represents the AlertManager webhook payload structure
type AlertManagerWebhook struct {
	Receiver          string                 `json:"receiver"`
	Status            string                 `json:"status"`
	Alerts            []AlertManagerAlert    `json:"alerts"`
	GroupLabels       map[string]string      `json:"groupLabels"`
	CommonLabels      map[string]string      `json:"commonLabels"`
	CommonAnnotations map[string]string      `json:"commonAnnotations"`
	ExternalURL       string                 `json:"externalURL"`
}

// AlertManagerAlert represents a single alert from AlertManager
type AlertManagerAlert struct {
	Status       string            `json:"status"`
	Labels       map[string]string `json:"labels"`
	Annotations  map[string]string `json:"annotations"`
	StartsAt     time.Time         `json:"startsAt"`
	EndsAt       time.Time         `json:"endsAt"`
	GeneratorURL string            `json:"generatorURL"`
	Fingerprint  string            `json:"fingerprint"`
}

// NewAlertManagerParser creates a new AlertManager parser
func NewAlertManagerParser() *AlertManagerParser {
	return &AlertManagerParser{}
}

// Parse parses AlertManager webhook data into normalized Alert structure
func (p *AlertManagerParser) Parse(data []byte, projectName string) (*models.AlertGroup, error) {
	var webhook AlertManagerWebhook
	if err := json.Unmarshal(data, &webhook); err != nil {
		return nil, fmt.Errorf("failed to unmarshal AlertManager webhook: %w", err)
	}

	// Convert to normalized alert group
	alertGroup := &models.AlertGroup{
		Receiver:          webhook.Receiver,
		Status:            models.AlertStatus(webhook.Status),
		GroupLabels:       webhook.GroupLabels,
		CommonLabels:      webhook.CommonLabels,
		CommonAnnotations: webhook.CommonAnnotations,
		ExternalURL:       webhook.ExternalURL,
		RawData:           make(map[string]interface{}),
	}

	// Store raw data
	rawData := make(map[string]interface{})
	json.Unmarshal(data, &rawData)
	alertGroup.RawData = rawData

	// Convert individual alerts
	for _, amAlert := range webhook.Alerts {
		alert := models.Alert{
			ID:          generateAlertID(amAlert.Fingerprint, projectName),
			Fingerprint: amAlert.Fingerprint,
			ProjectName: projectName,
			ProjectType: p.GetProjectType(),
			Status:      models.AlertStatus(amAlert.Status),
			Severity:    p.extractSeverity(amAlert.Labels),
			AlertName:   p.extractAlertName(amAlert.Labels),
			Message:     p.extractMessage(amAlert.Annotations),
			Labels:      amAlert.Labels,
			Annotations: amAlert.Annotations,
			StartsAt:    amAlert.StartsAt,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
			RawData:     make(map[string]interface{}),
		}

		// Set EndsAt if alert is resolved
		if amAlert.Status == "resolved" && !amAlert.EndsAt.IsZero() {
			alert.EndsAt = &amAlert.EndsAt
		}

		// Store raw alert data
		alertRawData := make(map[string]interface{})
		alertBytes, _ := json.Marshal(amAlert)
		json.Unmarshal(alertBytes, &alertRawData)
		alert.RawData = alertRawData

		alertGroup.Alerts = append(alertGroup.Alerts, alert)
	}

	return alertGroup, nil
}

// GetProjectType returns the project type for AlertManager
func (p *AlertManagerParser) GetProjectType() string {
	return "alertmanager"
}

// extractSeverity extracts severity from alert labels
func (p *AlertManagerParser) extractSeverity(labels map[string]string) models.AlertSeverity {
	if severity, exists := labels["severity"]; exists {
		switch severity {
		case "critical":
			return models.AlertSeverityError
		case "warning":
			return models.AlertSeverityWarning
		case "info":
			return models.AlertSeverityInfo
		default:
			return models.AlertSeverityInfo
		}
	}
	return models.AlertSeverityInfo
}

// extractAlertName extracts alert name from labels
func (p *AlertManagerParser) extractAlertName(labels map[string]string) string {
	if alertname, exists := labels["alertname"]; exists {
		return alertname
	}
	return "Unknown Alert"
}

// extractMessage extracts message from annotations
func (p *AlertManagerParser) extractMessage(annotations map[string]string) string {
	if summary, exists := annotations["summary"]; exists {
		return summary
	}
	if description, exists := annotations["description"]; exists {
		return description
	}
	if message, exists := annotations["message"]; exists {
		return message
	}
	return "No message available"
}

// generateAlertID generates a unique ID for the alert
func generateAlertID(fingerprint, projectName string) string {
	return fmt.Sprintf("%s_%s_%d", projectName, fingerprint, time.Now().Unix())
}
